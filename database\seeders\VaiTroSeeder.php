<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\VaiTro;

class VaiTroSeeder extends Seeder
{
    public function run(): void
    {
        VaiTro::insert([
            [
                'id' => 1,
                'ten' => 'SupperAdmin',
                'mo_ta' => 'Tà<PERSON> khoản quản trị cao nhất, có toàn quyền trong hệ thống.'
            ],
            [
                'id' => 2,
                'ten' => '<PERSON><PERSON>',
                'mo_ta' => 'Quản lý toàn bộ hoạt động của một chi nhánh cụ thể.'
            ],
            [
                'id' => 3,
                'ten' => 'Admin <PERSON>ạp',
                'mo_ta' => 'Quản lý một rạp phim trong chi nhánh.'
            ],
            [
                'id' => 4,
                'ten' => 'Nhân Viên',
                'mo_ta' => 'Nhân viên thực hiện các tác vụ hỗ trợ, bán vé, phụ<PERSON> vụ khách.'
            ],
            [
                'id' => 5,
                'ten' => '<PERSON><PERSON><PERSON><PERSON>àng',
                'mo_ta' => '<PERSON><PERSON><PERSON><PERSON> dùng hệ thống, có thể đặt vé, xem lịch chiếu, đánh giá phim.'
            ],
        ]);
    }
}
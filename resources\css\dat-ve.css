.khung {
        box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, 
                    rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;
        width: 1250px;
        margin: 20px auto; 
        padding: 20px; 
        background-color: #fff;
        border-radius: 8px;
        height: auto;
    }
  .box-select {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 20px;
}

.box-select select {
    padding: 10px 15px;
    font-size: 16px;
    border-radius: 8px;
    border: 1px solid #ccc;
    background-color: #fff;
    color: #333;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
    transition: border-color 0.3s, box-shadow 0.3s;
    min-width: 220px;
}

.box-select select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.2);
}

.box-select option {
    padding: 10px;
}


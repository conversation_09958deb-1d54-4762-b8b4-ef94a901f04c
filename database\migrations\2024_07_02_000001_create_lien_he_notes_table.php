<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lien_he_notes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lien_he_id')->constrained('lien_hes')->onDelete('cascade');
            $table->text('noi_dung');
            $table->string('nguoi_tao', 100)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lien_he_notes');
    }
};

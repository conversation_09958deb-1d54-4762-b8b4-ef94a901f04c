<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('loai_phongs', function (Blueprint $table) {
            $table->decimal('phu_thu', 10, 2)->default(0)->after('mo_ta'); 
        });
    }

    public function down(): void
    {
        Schema::table('loai_phongs', function (Blueprint $table) {
            $table->dropColumn('phu_thu');
        });
    }
};
